import { Request, Response } from 'express';
import logger from '../utils/logger';
import { sendSuccess, sendError } from '../utils/response';
import { 
  simulateRealTimeTransaction,
  RealTimeSimulationConfig 
} from '../services/realTimeTransactionSimulator';
import { getPendingTransfers } from '../services/plaidTransferSimulationService';
import { isSimulationAvailable } from '../services/plaidTransferSimulationService';

/**
 * Simulate a single transaction with real-time production-like behavior
 */
export const simulateRealTimeTransactionController = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Real-time simulation is only available in sandbox environment', 403);
    }

    const { transferId } = req.params;
    const config: Partial<RealTimeSimulationConfig> = req.body;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    logger.info('Starting real-time transaction simulation', { 
      transferId, 
      config 
    });

    const result = await simulateRealTimeTransaction(transferId, config);

    if (result.success) {
      return sendSuccess(res, {
        transferId: result.transferId,
        finalStatus: result.status,
        timeline: result.timeline,
        webhooksDelivered: result.webhooksDelivered,
        summary: {
          totalSteps: result.timeline.length,
          webhooksTriggered: result.webhooksDelivered.length,
          processingTime: result.timeline.length > 0 
            ? new Date(result.timeline[result.timeline.length - 1].timestamp).getTime() - 
              new Date(result.timeline[0].timestamp).getTime()
            : 0,
          finalStatus: result.status
        }
      }, result.message);
    } else {
      return sendError(res, result.message, 400, result.error);
    }

  } catch (error) {
    logger.error('Error in real-time simulation controller', {
      error: error instanceof Error ? error.message : 'Unknown error',
      transferId: req.params.transferId
    });
    return sendError(res, 'Failed to simulate real-time transaction', 500);
  }
};

/**
 * Simulate all pending transfers with real-time behavior
 */
export const simulateAllPendingRealTimeController = async (req: Request, res: Response) => {
  try {
    if (!isSimulationAvailable()) {
      return sendError(res, 'Real-time simulation is only available in sandbox environment', 403);
    }

    const config: Partial<RealTimeSimulationConfig> = req.body;

    logger.info('Starting real-time simulation for all pending transfers', { config });

    // Get all pending transfers
    const pendingTransfers = await getPendingTransfers();
    
    if (pendingTransfers.length === 0) {
      return sendSuccess(res, {
        message: 'No pending transfers found to simulate',
        results: [],
        summary: { total: 0, succeeded: 0, failed: 0, totalAmount: 0 }
      }, 'No pending transfers to simulate');
    }

    const results = [];
    let succeeded = 0;
    let failed = 0;
    let totalAmount = 0;

    // Process each transfer with real-time simulation
    for (const transfer of pendingTransfers) {
      try {
        const amount = Math.abs(parseFloat(transfer.amount.toString()));
        totalAmount += amount;

        logger.info('Simulating real-time transaction', { 
          transferId: transfer.reference_id,
          amount 
        });

        const result = await simulateRealTimeTransaction(transfer.reference_id, config);
        results.push(result);

        if (result.success && (result.status === 'posted' || result.status === 'settled')) {
          succeeded++;
        } else {
          failed++;
        }

        // Add delay between transactions to simulate realistic processing
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        failed++;
        logger.error('Error simulating individual transfer', {
          transferId: transfer.reference_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const summary = {
      total: pendingTransfers.length,
      succeeded,
      failed,
      totalAmount,
      formattedTotalAmount: `$${totalAmount.toFixed(2)}`
    };

    logger.info('Real-time simulation completed for all pending transfers', summary);

    return sendSuccess(res, {
      message: `Real-time simulation completed for ${pendingTransfers.length} transfers`,
      results,
      summary,
      details: {
        processingTime: results.reduce((total, result) => {
          if (result.timeline.length > 0) {
            return total + (
              new Date(result.timeline[result.timeline.length - 1].timestamp).getTime() - 
              new Date(result.timeline[0].timestamp).getTime()
            );
          }
          return total;
        }, 0),
        totalWebhooks: results.reduce((total, result) => total + result.webhooksDelivered.length, 0)
      }
    }, `Real-time simulation completed: ${succeeded} succeeded, ${failed} failed`);

  } catch (error) {
    logger.error('Error in simulate all pending real-time controller', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to simulate all pending transfers', 500);
  }
};

/**
 * Get real-time simulation configuration options
 */
export const getRealTimeSimulationConfigController = async (req: Request, res: Response) => {
  try {
    const defaultConfig = {
      enableRealisticTiming: true,
      enableWebhookDelivery: true,
      enableStatusTransitions: true,
      enableFailureSimulation: true,
      processingDelayMs: 2000,
      webhookDelayMs: 500,
      failureRate: 0.05
    };

    const configOptions = {
      enableRealisticTiming: {
        description: 'Enable realistic processing delays (simulates ACH processing time)',
        type: 'boolean',
        default: true
      },
      enableWebhookDelivery: {
        description: 'Enable webhook delivery simulation',
        type: 'boolean',
        default: true
      },
      enableStatusTransitions: {
        description: 'Enable intermediate status transitions (pending -> processing -> posted/failed)',
        type: 'boolean',
        default: true
      },
      enableFailureSimulation: {
        description: 'Enable random transaction failures based on failure rate',
        type: 'boolean',
        default: true
      },
      processingDelayMs: {
        description: 'Processing delay in milliseconds (simulates bank processing time)',
        type: 'number',
        default: 2000,
        min: 100,
        max: 10000
      },
      webhookDelayMs: {
        description: 'Webhook delivery delay in milliseconds',
        type: 'number',
        default: 500,
        min: 0,
        max: 5000
      },
      failureRate: {
        description: 'Failure rate (0.0 to 1.0, where 0.05 = 5% failure rate)',
        type: 'number',
        default: 0.05,
        min: 0.0,
        max: 1.0
      }
    };

    return sendSuccess(res, {
      defaultConfig,
      configOptions,
      examples: {
        quickTest: {
          enableRealisticTiming: false,
          processingDelayMs: 100,
          webhookDelayMs: 50
        },
        productionLike: {
          enableRealisticTiming: true,
          enableWebhookDelivery: true,
          enableStatusTransitions: true,
          enableFailureSimulation: true,
          processingDelayMs: 5000,
          webhookDelayMs: 1000,
          failureRate: 0.02
        },
        highFailureRate: {
          enableFailureSimulation: true,
          failureRate: 0.3
        }
      }
    }, 'Real-time simulation configuration retrieved successfully');

  } catch (error) {
    logger.error('Error getting real-time simulation config', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to get simulation configuration', 500);
  }
};

/**
 * Get transaction timeline for a specific transfer
 */
export const getTransactionTimelineController = async (req: Request, res: Response) => {
  try {
    const { transferId } = req.params;

    if (!transferId) {
      return sendError(res, 'Transfer ID is required', 400);
    }

    // Get webhook events for this transfer
    const webhookEvents = await getWebhookEventsForTransfer(transferId);
    
    // Get transaction status history
    const statusHistory = await getTransactionStatusHistory(transferId);

    return sendSuccess(res, {
      transferId,
      webhookEvents,
      statusHistory,
      summary: {
        totalWebhooks: webhookEvents.length,
        statusChanges: statusHistory.length,
        currentStatus: statusHistory.length > 0 ? statusHistory[statusHistory.length - 1].status : 'unknown'
      }
    }, 'Transaction timeline retrieved successfully');

  } catch (error) {
    logger.error('Error getting transaction timeline', {
      transferId: req.params.transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return sendError(res, 'Failed to get transaction timeline', 500);
  }
};

/**
 * Helper function to get webhook events for a transfer
 */
async function getWebhookEventsForTransfer(transferId: string) {
  try {
    const { executeQuery } = require('../utils/database');
    
    const events = await executeQuery(
      `SELECT webhook_type, webhook_code, event_id, status, raw_data, created_at
       FROM tbl_webhook_events
       WHERE transfer_id = ?
       ORDER BY created_at ASC`,
      [transferId]
    );

    return Array.isArray(events) ? events : [];
  } catch (error) {
    logger.error('Error getting webhook events', { transferId, error });
    return [];
  }
}

/**
 * Helper function to get transaction status history
 */
async function getTransactionStatusHistory(transferId: string) {
  try {
    const { executeQuery } = require('../utils/database');
    
    const history = await executeQuery(
      `SELECT status_id, meta_data, last_updated, created_at
       FROM tbl_wallet_transactions
       WHERE reference_id = ?
       ORDER BY last_updated ASC`,
      [transferId]
    );

    return Array.isArray(history) ? history.map(record => ({
      status: getStatusName(record.status_id),
      statusId: record.status_id,
      metadata: typeof record.meta_data === 'string' ? JSON.parse(record.meta_data) : record.meta_data,
      timestamp: record.last_updated || record.created_at
    })) : [];
  } catch (error) {
    logger.error('Error getting transaction status history', { transferId, error });
    return [];
  }
}

/**
 * Convert status ID to status name
 */
function getStatusName(statusId: number): string {
  switch (statusId) {
    case 1: return 'completed';
    case 2: return 'pending';
    case 3: return 'failed';
    default: return 'unknown';
  }
}
