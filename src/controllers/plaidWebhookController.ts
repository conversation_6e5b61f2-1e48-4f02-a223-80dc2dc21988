import { Request, Response } from 'express';
import {
  confirmPendingDeposit,
  confirmWithdrawalHold,
  releaseFailedHold
} from '../services/enhancedPendingBalanceService';
import { updateTransferStatus } from '../services/plaidTransferService';
import { executeUpdate, executeQuerySingle } from '../utils/database';
import logger from '../utils/logger';
import { sendSuccess, sendError } from '../utils/response';

/**
 * Handle Plaid webhook events for transfer status updates
 * This is the real-time way to get notified when transfers complete/fail
 */
export const handlePlaidWebhook = async (req: Request, res: Response) => {
  try {
    const webhookEvent = req.body;

    logger.info('Received Plaid webhook', {
      webhookType: webhookEvent.webhook_type,
      webhookCode: webhookEvent.webhook_code,
      itemId: webhookEvent.item_id,
      transferId: webhookEvent.transfer_id,
      eventId: webhookEvent.event_id
    });

    // Store webhook event for audit and debugging
    await storeWebhookEvent(webhookEvent);

    // Handle transfer-related webhooks
    if (webhookEvent.webhook_type === 'TRANSFER') {
      await handleTransferWebhook(webhookEvent);
    }

    // Always respond with 200 to acknowledge receipt
    sendSuccess(res, { received: true }, 'Webhook processed successfully');

  } catch (error) {
    logger.error('Error processing Plaid webhook', {
      error: error instanceof Error ? error.message : 'Unknown error',
      webhook: req.body
    });

    // Still return 200 to prevent Plaid from retrying
    sendSuccess(res, { received: true, error: 'Processing failed but acknowledged' }, 'Webhook acknowledged');
  }
};

/**
 * Store webhook event for audit and debugging
 */
async function storeWebhookEvent(webhookEvent: any) {
  try {
    await executeUpdate(
      `INSERT INTO tbl_webhook_events
       (webhook_type, webhook_code, event_id, transfer_id, item_id, account_id, status, raw_data, created_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        webhookEvent.webhook_type || null,
        webhookEvent.webhook_code || null,
        webhookEvent.event_id || null,
        webhookEvent.transfer_id || null,
        webhookEvent.item_id || null,
        webhookEvent.account_id || null,
        webhookEvent.status || null,
        JSON.stringify(webhookEvent)
      ]
    );
  } catch (error) {
    logger.error('Error storing webhook event', {
      error: error instanceof Error ? error.message : 'Unknown error',
      webhookEvent
    });
  }
}

/**
 * Handle transfer-specific webhook events
 */
async function handleTransferWebhook(webhookEvent: any) {
  const { webhook_code, transfer_id, event_id } = webhookEvent;

  if (!transfer_id) {
    logger.warn('Transfer webhook received without transfer_id', { webhookEvent });
    return;
  }

  logger.info('Processing transfer webhook', {
    webhookCode: webhook_code,
    transferId: transfer_id,
    eventId: event_id
  });

  try {
    switch (webhook_code) {
      case 'TRANSFER_EVENTS_UPDATE':
        // Transfer status has changed
        await handleTransferStatusUpdate(transfer_id, webhookEvent);
        break;
        
      case 'TRANSFER_POSTED':
        // Transfer has been posted (completed)
        await handleTransferPosted(transfer_id, webhookEvent);
        break;

      case 'TRANSFER_SETTLED':
        // Transfer has been settled (final completion)
        await handleTransferSettled(transfer_id, webhookEvent);
        break;

      case 'TRANSFER_FAILED':
        // Transfer has failed
        await handleTransferFailed(transfer_id, webhookEvent);
        break;

      case 'TRANSFER_CANCELLED':
        // Transfer was cancelled
        await handleTransferCancelled(transfer_id, webhookEvent);
        break;
        
      case 'TRANSFER_RETURNED':
        // Transfer was returned
        await handleTransferReturned(transfer_id, webhookEvent);
        break;
        
      default:
        logger.info('Unhandled transfer webhook code', {
          webhookCode: webhook_code,
          transferId: transfer_id
        });
    }
  } catch (error) {
    logger.error('Error handling transfer webhook', {
      webhookCode: webhook_code,
      transferId: transfer_id,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle general transfer status updates
 */
async function handleTransferStatusUpdate(transferId: string, webhookEvent: any) {
  try {
    // Get the latest status from Plaid
    const { getTransferStatus } = await import('../services/plaidTransferService');
    const statusResult = await getTransferStatus(transferId);

    if (statusResult.success && statusResult.status) {
      // Update the database status with event correlation
      await updateTransferStatusWithEvent(transferId, statusResult.status, statusResult.failureReason, webhookEvent.event_id);

      // Process pending holds based on status
      if (statusResult.status === 'posted' || statusResult.status === 'settled') {
        await handleTransferSuccess(transferId, statusResult.status);
      } else if (statusResult.status === 'failed' || statusResult.status === 'cancelled' || statusResult.status === 'returned') {
        await handleTransferFailure(transferId, statusResult.failureReason || 'Transfer failed');
      }

      logger.info('Transfer status updated via webhook', {
        transferId,
        status: statusResult.status,
        eventId: webhookEvent.event_id
      });
    }
  } catch (error) {
    logger.error('Error handling transfer status update', {
      transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle successful transfer completion
 */
async function handleTransferSuccess(transferId: string, status: string) {
  try {
    // Try to confirm withdrawal hold (for staff payments and withdrawals)
    const withdrawalResult = await confirmWithdrawalHold(transferId, status);

    if (withdrawalResult.success) {
      logger.info('Withdrawal hold confirmed via webhook', {
        transferId,
        status,
        userId: withdrawalResult.userId,
        amount: withdrawalResult.amount
      });
      return;
    }

    // Try to confirm deposit (for add money)
    const depositResult = await confirmPendingDeposit(transferId, status);

    if (depositResult.success) {
      logger.info('Deposit confirmed via webhook', {
        transferId,
        status,
        userId: depositResult.userId,
        amount: depositResult.amount
      });
      return;
    }

    // If neither worked, just log it
    logger.info('Transfer completed but no pending hold found', {
      transferId,
      status
    });

  } catch (error) {
    logger.error('Error handling transfer success', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle transfer failure
 */
async function handleTransferFailure(transferId: string, failureReason: string) {
  try {
    // Release any failed holds
    const releaseResult = await releaseFailedHold(transferId, failureReason);

    if (releaseResult.success) {
      logger.info('Failed hold released via webhook', {
        transferId,
        failureReason,
        userId: releaseResult.userId,
        amount: releaseResult.amount
      });
    } else {
      logger.info('Transfer failed but no pending hold found', {
        transferId,
        failureReason
      });
    }

  } catch (error) {
    logger.error('Error handling transfer failure', {
      transferId,
      failureReason,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle transfer posted event
 */
async function handleTransferPosted(transferId: string, webhookEvent?: any) {
  // Update status with event correlation
  await updateTransferStatusWithEvent(transferId, 'posted', undefined, webhookEvent?.event_id);
  await handleTransferSuccess(transferId, 'posted');
}

/**
 * Handle transfer settled event
 */
async function handleTransferSettled(transferId: string, webhookEvent?: any) {
  // Update status with event correlation
  await updateTransferStatusWithEvent(transferId, 'settled', undefined, webhookEvent?.event_id);
  await handleTransferSuccess(transferId, 'settled');
}

/**
 * Handle transfer failed event
 */
async function handleTransferFailed(transferId: string, webhookEvent: any) {
  const failureReason = webhookEvent.failure_reason?.description || 'Transfer failed';
  // Update status with event correlation
  await updateTransferStatusWithEvent(transferId, 'failed', failureReason, webhookEvent?.event_id);
  await handleTransferFailure(transferId, failureReason);
}

/**
 * Handle transfer cancelled event
 */
async function handleTransferCancelled(transferId: string, webhookEvent?: any) {
  // Update status with event correlation
  await updateTransferStatusWithEvent(transferId, 'cancelled', 'Transfer cancelled', webhookEvent?.event_id);
  await handleTransferFailure(transferId, 'Transfer cancelled');
}

/**
 * Handle transfer returned event
 */
async function handleTransferReturned(transferId: string, webhookEvent: any) {
  const returnReason = webhookEvent.return_reason?.description || 'Transfer returned';
  await handleTransferFailure(transferId, `Transfer returned: ${returnReason}`);
}

/**
 * Update transfer status with plaid_event_id for correlation
 */
async function updateTransferStatusWithEvent(
  transferId: string,
  status: string,
  failureReason?: string,
  eventId?: string
): Promise<void> {
  try {
    const statusId = status === 'posted' || status === 'settled' ? 1 : (status === 'failed' ? 3 : 2);

    // First, get the current transaction to preserve existing metadata
    const transaction = await executeQuerySingle(
      `SELECT meta_data FROM tbl_wallet_transactions WHERE reference_id = ?`,
      [transferId]
    );

    const existingMetadata = transaction?.meta_data
      ? (typeof transaction.meta_data === 'string' ? JSON.parse(transaction.meta_data) : transaction.meta_data)
      : {};

    const updatedMetadata = {
      ...existingMetadata,
      plaid_status: status,
      plaid_event_id: eventId,
      webhook_processed_at: new Date().toISOString(),
      ...(failureReason && { failure_reason: failureReason })
    };

    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = ?,
           meta_data = ?,
           plaid_event_id = ?,
           last_updated = NOW()
       WHERE reference_id = ?`,
      [
        statusId,
        JSON.stringify(updatedMetadata),
        eventId || null,
        transferId
      ]
    );

    logger.info('Transfer status updated with event correlation', {
      transferId,
      status,
      statusId,
      eventId,
      failureReason
    });

  } catch (error) {
    logger.error('Error updating transfer status with event', {
      transferId,
      status,
      eventId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    // Fallback to original update method
    await updateTransferStatus(transferId, status, failureReason);
  }
}