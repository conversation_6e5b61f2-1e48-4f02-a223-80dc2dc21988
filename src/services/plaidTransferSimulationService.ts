import { plaidClient, plaidClientId, plaidSecret, plaidEnv } from '../config/plaidConfig';
import logger from '../utils/logger';
import { executeQuerySingle, executeUpdate } from '../utils/database';

export interface TransferSimulationResult {
  success: boolean;
  message: string;
  transferId?: string;
  eventType?: string;
  error?: string;
}

export interface TransferSimulationRequest {
  transferId: string;
  eventType: 'posted' | 'failed';
  failureReason?: string;
}

/**
 * Simulate transfer completion or failure using Plaid's sandbox endpoint
 * This is only available in sandbox environment
 */
export async function simulateTransferEvent(
  transferId: string,
  eventType: 'posted' | 'failed',
  failureReason?: string
): Promise<TransferSimulationResult> {
  try {
    // Only allow simulation in sandbox environment
    if (plaidEnv !== 'sandbox') {
      return {
        success: false,
        message: 'Transfer simulation is only available in sandbox environment',
        error: 'ENVIRONMENT_NOT_SUPPORTED'
      };
    }

    // Validate transfer exists in our database
    const transfer = await executeQuerySingle(
      `SELECT * FROM tbl_wallet_transactions WHERE reference_id = ? AND payment_provider LIKE '%plaid%'`,
      [transferId]
    );

    if (!transfer) {
      return {
        success: false,
        message: 'Transfer not found in database',
        error: 'TRANSFER_NOT_FOUND'
      };
    }

    logger.info('Simulating transfer event', {
      transferId,
      eventType,
      failureReason,
      currentStatus: transfer.status_id
    });

    // Call Plaid's sandbox simulation endpoint
    const simulationRequest = {
      client_id: plaidClientId,
      secret: plaidSecret,
      transfer_id: transferId,
      event_type: eventType,
      ...(failureReason && { failure_reason: failureReason })
    };

    const response = await fetch('https://sandbox.plaid.com/sandbox/transfer/simulate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(simulationRequest)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logger.error('Plaid simulation API error', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      });

      return {
        success: false,
        message: `Plaid simulation failed: ${response.statusText}`,
        error: 'PLAID_API_ERROR'
      };
    }

    const result = await response.json();
    
    logger.info('Transfer simulation successful', {
      transferId,
      eventType,
      result
    });

    // Update local status immediately for testing purposes
    // The webhook should also update this, but this ensures immediate feedback
    await updateLocalTransferStatus(transferId, eventType, failureReason);

    return {
      success: true,
      message: `Transfer ${eventType} simulation triggered successfully`,
      transferId,
      eventType
    };

  } catch (error) {
    logger.error('Error simulating transfer event', {
      transferId,
      eventType,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to simulate transfer event',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update transfer status locally for immediate feedback
 */
async function updateLocalTransferStatus(
  transferId: string,
  eventType: 'posted' | 'failed',
  failureReason?: string
): Promise<void> {
  try {
    const statusId = eventType === 'posted' ? 1 : 3; // 1=completed, 3=failed
    
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = ?,
           meta_data = JSON_SET(
             COALESCE(meta_data, '{}'),
             '$.plaid_status', ?,
             '$.simulation_triggered', true,
             '$.simulation_timestamp', ?,
             '$.failure_reason', ?
           ),
           last_updated = NOW()
       WHERE reference_id = ?`,
      [
        statusId,
        eventType,
        new Date().toISOString(),
        failureReason || null,
        transferId
      ]
    );

    logger.info('Local transfer status updated', {
      transferId,
      eventType,
      statusId,
      failureReason
    });

  } catch (error) {
    logger.error('Error updating local transfer status', {
      transferId,
      eventType,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Get all pending transfers that can be simulated
 */
export async function getPendingTransfers(): Promise<any[]> {
  try {
    const transfers = await executeQuerySingle(
      `SELECT reference_id, user_id, amount, description, created_at, meta_data
       FROM tbl_wallet_transactions
       WHERE status_id = 2 
       AND payment_provider LIKE '%plaid%'
       AND reference_id IS NOT NULL
       ORDER BY created_at DESC
       LIMIT 50`
    );

    return Array.isArray(transfers) ? transfers : (transfers ? [transfers] : []);

  } catch (error) {
    logger.error('Error getting pending transfers', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return [];
  }
}

/**
 * Simulate multiple transfers at once (for bulk testing)
 */
export async function simulateMultipleTransfers(
  requests: TransferSimulationRequest[]
): Promise<TransferSimulationResult[]> {
  const results: TransferSimulationResult[] = [];

  for (const request of requests) {
    const result = await simulateTransferEvent(
      request.transferId,
      request.eventType,
      request.failureReason
    );
    results.push(result);

    // Add small delay between requests to avoid rate limiting
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return results;
}

/**
 * Validate if transfer simulation is available
 */
export function isSimulationAvailable(): boolean {
  return plaidEnv === 'sandbox';
}

/**
 * Get simulation environment info
 */
export function getSimulationInfo() {
  return {
    available: isSimulationAvailable(),
    environment: plaidEnv,
    clientId: plaidClientId,
    message: isSimulationAvailable()
      ? 'Transfer simulation is available in sandbox mode'
      : 'Transfer simulation is only available in sandbox environment'
  };
}

/**
 * Settle all pending transfers by simulating success for all pending transfers
 */
export async function settleAllPendingTransfers(): Promise<{
  success: boolean;
  message: string;
  results: TransferSimulationResult[];
  summary: {
    total: number;
    succeeded: number;
    failed: number;
    totalAmount: number;
  };
}> {
  try {
    // Only allow in sandbox environment
    if (plaidEnv !== 'sandbox') {
      return {
        success: false,
        message: 'Settlement simulation is only available in sandbox environment',
        results: [],
        summary: { total: 0, succeeded: 0, failed: 0, totalAmount: 0 }
      };
    }

    // Get all pending transfers
    const pendingTransfers = await getPendingTransfers();

    if (pendingTransfers.length === 0) {
      return {
        success: true,
        message: 'No pending transfers found to settle',
        results: [],
        summary: { total: 0, succeeded: 0, failed: 0, totalAmount: 0 }
      };
    }

    logger.info('Starting settlement of all pending transfers', {
      count: pendingTransfers.length
    });

    const results: TransferSimulationResult[] = [];
    let succeeded = 0;
    let failed = 0;
    let totalAmount = 0;

    // Process each transfer
    for (const transfer of pendingTransfers) {
      try {
        const amount = Math.abs(parseFloat(transfer.amount.toString()));
        totalAmount += amount;

        // Simulate success for each transfer
        const result = await simulateTransferEvent(
          transfer.reference_id,
          'posted'
        );

        results.push(result);

        if (result.success) {
          succeeded++;
          logger.info('Transfer settled successfully', {
            transferId: transfer.reference_id,
            amount
          });
        } else {
          failed++;
          logger.warn('Transfer settlement failed', {
            transferId: transfer.reference_id,
            error: result.error
          });
        }

        // Add small delay between requests to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));

      } catch (error) {
        failed++;
        const errorResult: TransferSimulationResult = {
          success: false,
          message: `Failed to settle transfer ${transfer.reference_id}`,
          transferId: transfer.reference_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        results.push(errorResult);

        logger.error('Error settling individual transfer', {
          transferId: transfer.reference_id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const summary = {
      total: pendingTransfers.length,
      succeeded,
      failed,
      totalAmount
    };

    logger.info('Settlement process completed', summary);

    return {
      success: succeeded > 0,
      message: succeeded === pendingTransfers.length
        ? `Successfully settled all ${succeeded} pending transfers ($${totalAmount.toFixed(2)})`
        : `Settled ${succeeded} of ${pendingTransfers.length} transfers. ${failed} failed.`,
      results,
      summary
    };

  } catch (error) {
    logger.error('Error in settle all pending transfers', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      message: 'Failed to settle pending transfers',
      results: [],
      summary: { total: 0, succeeded: 0, failed: 0, totalAmount: 0 }
    };
  }
}
