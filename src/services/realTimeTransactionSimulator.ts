import { plaidClient } from '../config/plaidConfig';
import { executeQuery, executeUpdate } from '../utils/database';
import logger from '../utils/logger';
import { getTransferStatus } from './plaidTransferService';

/**
 * Real-Time Transaction Simulator
 * Simulates production-like behavior with realistic timing, webhooks, and status transitions
 */

export interface RealTimeSimulationConfig {
  enableRealisticTiming: boolean;
  enableWebhookDelivery: boolean;
  enableStatusTransitions: boolean;
  enableFailureSimulation: boolean;
  processingDelayMs: number;
  webhookDelayMs: number;
  failureRate: number; // 0.0 to 1.0 (percentage of transactions that should fail)
}

export interface TransactionSimulationResult {
  success: boolean;
  transferId: string;
  status: string;
  timeline: TransactionTimeline[];
  webhooksDelivered: WebhookDelivery[];
  message: string;
  error?: string;
}

export interface TransactionTimeline {
  timestamp: Date;
  status: string;
  description: string;
  webhookTriggered: boolean;
}

export interface WebhookDelivery {
  timestamp: Date;
  webhookType: string;
  webhookCode: string;
  eventId: string;
  delivered: boolean;
  responseTime: number;
}

/**
 * Default configuration for realistic simulation
 */
const DEFAULT_CONFIG: RealTimeSimulationConfig = {
  enableRealisticTiming: true,
  enableWebhookDelivery: true,
  enableStatusTransitions: true,
  enableFailureSimulation: true,
  processingDelayMs: 2000, // 2 seconds for processing
  webhookDelayMs: 500, // 500ms webhook delivery delay
  failureRate: 0.05 // 5% failure rate
};

/**
 * Simulate a real-time transaction with production-like behavior
 */
export async function simulateRealTimeTransaction(
  transferId: string,
  config: Partial<RealTimeSimulationConfig> = {}
): Promise<TransactionSimulationResult> {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  const timeline: TransactionTimeline[] = [];
  const webhooksDelivered: WebhookDelivery[] = [];

  try {
    logger.info('Starting real-time transaction simulation', { 
      transferId, 
      config: finalConfig 
    });

    // Step 1: Initial status check
    timeline.push({
      timestamp: new Date(),
      status: 'pending',
      description: 'Transaction initiated and pending processing',
      webhookTriggered: false
    });

    // Step 2: Simulate processing delay (like real ACH processing)
    if (finalConfig.enableRealisticTiming) {
      await new Promise(resolve => setTimeout(resolve, finalConfig.processingDelayMs));
    }

    // Step 3: Determine if transaction should fail (based on failure rate)
    const shouldFail = finalConfig.enableFailureSimulation && 
                      Math.random() < finalConfig.failureRate;

    // Step 4: Process transaction with status transitions
    if (finalConfig.enableStatusTransitions) {
      // Simulate intermediate status: "processing"
      await updateTransactionStatus(transferId, 'processing');
      timeline.push({
        timestamp: new Date(),
        status: 'processing',
        description: 'Transaction is being processed by the bank',
        webhookTriggered: false
      });

      // Deliver webhook for processing status
      if (finalConfig.enableWebhookDelivery) {
        const webhook = await deliverWebhook(transferId, 'TRANSFER', 'TRANSFER_PROCESSING', finalConfig.webhookDelayMs);
        webhooksDelivered.push(webhook);
      }

      // Additional processing delay
      if (finalConfig.enableRealisticTiming) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Step 5: Final status determination
    const finalStatus = shouldFail ? 'failed' : 'posted';
    const failureReason = shouldFail ? getRandomFailureReason() : undefined;

    // Step 6: Simulate the actual Plaid API call
    const plaidResult = await simulateTransferEvent(transferId, finalStatus, failureReason);

    if (!plaidResult.success) {
      return {
        success: false,
        transferId,
        status: 'error',
        timeline,
        webhooksDelivered,
        message: 'Failed to simulate transfer event',
        error: plaidResult.error
      };
    }

    // Step 7: Update final status
    await updateTransactionStatus(transferId, finalStatus);
    timeline.push({
      timestamp: new Date(),
      status: finalStatus,
      description: finalStatus === 'posted' 
        ? 'Transaction completed successfully'
        : `Transaction failed: ${failureReason}`,
      webhookTriggered: true
    });

    // Step 8: Deliver final webhook
    if (finalConfig.enableWebhookDelivery) {
      const webhookCode = finalStatus === 'posted' ? 'TRANSFER_POSTED' : 'TRANSFER_FAILED';
      const webhook = await deliverWebhook(transferId, 'TRANSFER', webhookCode, finalConfig.webhookDelayMs);
      webhooksDelivered.push(webhook);
    }

    // Step 9: If successful, simulate settlement (for posted transactions)
    if (finalStatus === 'posted' && finalConfig.enableStatusTransitions) {
      // Simulate settlement delay (typically 1-2 business days, but we'll use 3 seconds)
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      await updateTransactionStatus(transferId, 'settled');
      timeline.push({
        timestamp: new Date(),
        status: 'settled',
        description: 'Transaction has been settled and funds are available',
        webhookTriggered: true
      });

      // Deliver settlement webhook
      if (finalConfig.enableWebhookDelivery) {
        const webhook = await deliverWebhook(transferId, 'TRANSFER', 'TRANSFER_SETTLED', finalConfig.webhookDelayMs);
        webhooksDelivered.push(webhook);
      }
    }

    logger.info('Real-time transaction simulation completed', {
      transferId,
      finalStatus,
      timelineEvents: timeline.length,
      webhooksDelivered: webhooksDelivered.length
    });

    return {
      success: true,
      transferId,
      status: finalStatus,
      timeline,
      webhooksDelivered,
      message: `Transaction simulation completed with status: ${finalStatus}`
    };

  } catch (error) {
    logger.error('Error in real-time transaction simulation', {
      transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      success: false,
      transferId,
      status: 'error',
      timeline,
      webhooksDelivered,
      message: 'Simulation failed due to error',
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Simulate transfer event using Plaid API
 */
async function simulateTransferEvent(
  transferId: string,
  eventType: 'posted' | 'failed',
  failureReason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    await plaidClient.sandboxTransferSimulate({
      transfer_id: transferId,
      event_type: eventType,
      failure_reason: failureReason as any
    });

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Update transaction status in database
 */
async function updateTransactionStatus(transferId: string, status: string): Promise<void> {
  try {
    const statusId = getStatusId(status);
    
    await executeUpdate(
      `UPDATE tbl_wallet_transactions
       SET status_id = ?,
           meta_data = JSON_SET(meta_data, '$.plaid_status', ?, '$.last_updated', NOW()),
           last_updated = NOW()
       WHERE reference_id = ?`,
      [statusId, status, transferId]
    );

    logger.debug('Transaction status updated', { transferId, status, statusId });
  } catch (error) {
    logger.error('Error updating transaction status', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Convert status string to status ID
 */
function getStatusId(status: string): number {
  switch (status) {
    case 'posted':
    case 'settled':
      return 1; // completed
    case 'pending':
    case 'processing':
      return 2; // pending
    case 'failed':
    case 'cancelled':
    case 'returned':
      return 3; // failed
    default:
      return 2; // default to pending
  }
}

/**
 * Get random failure reason for realistic simulation
 */
function getRandomFailureReason(): string {
  const reasons = [
    'insufficient_funds',
    'account_closed',
    'invalid_account_number',
    'unauthorized',
    'payment_stopped',
    'customer_initiated_return',
    'bank_account_restricted'
  ];
  
  return reasons[Math.floor(Math.random() * reasons.length)];
}

/**
 * Simulate webhook delivery
 */
async function deliverWebhook(
  transferId: string,
  webhookType: string,
  webhookCode: string,
  delayMs: number
): Promise<WebhookDelivery> {
  const startTime = Date.now();
  
  try {
    // Simulate webhook delivery delay
    await new Promise(resolve => setTimeout(resolve, delayMs));
    
    // Generate event ID
    const eventId = `evt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    
    // Simulate webhook delivery to our own endpoint
    const webhookPayload = {
      webhook_type: webhookType,
      webhook_code: webhookCode,
      transfer_id: transferId,
      event_id: eventId,
      timestamp: new Date().toISOString()
    };

    // Store webhook event for audit
    await executeUpdate(
      `INSERT INTO tbl_webhook_events
       (webhook_type, webhook_code, event_id, transfer_id, status, raw_data, created_at)
       VALUES (?, ?, ?, ?, ?, ?, NOW())`,
      [
        webhookType,
        webhookCode,
        eventId,
        transferId,
        'delivered',
        JSON.stringify(webhookPayload)
      ]
    );

    const responseTime = Date.now() - startTime;
    
    logger.info('Webhook delivered', {
      transferId,
      webhookType,
      webhookCode,
      eventId,
      responseTime
    });

    return {
      timestamp: new Date(),
      webhookType,
      webhookCode,
      eventId,
      delivered: true,
      responseTime
    };

  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    logger.error('Webhook delivery failed', {
      transferId,
      webhookType,
      webhookCode,
      error: error instanceof Error ? error.message : 'Unknown error'
    });

    return {
      timestamp: new Date(),
      webhookType,
      webhookCode,
      eventId: 'failed',
      delivered: false,
      responseTime
    };
  }
}
