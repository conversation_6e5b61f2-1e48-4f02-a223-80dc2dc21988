import express, { Request, Response, NextFunction } from 'express';
import {
  simulateRealTimeTransactionController,
  simulateAllPendingRealTimeController,
  getRealTimeSimulationConfigController,
  getTransactionTimelineController
} from '../controllers/realTimeSimulationController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { plaidEnv } from '../config/plaidConfig';
import { sendError } from '../utils/response';

const router = express.Router();

/**
 * Middleware to ensure real-time simulation endpoints are only available in sandbox/development
 */
const ensureSandboxEnvironment = (req: Request, res: Response, next: NextFunction) => {
  if (plaidEnv !== 'sandbox' && process.env.NODE_ENV !== 'development') {
    return sendError(res, 'Real-time simulation is only available in sandbox/development environment', 403);
  }
  next();
};

/**
 * Middleware to add real-time simulation headers for identification
 */
const addRealTimeSimulationHeaders = (req: Request, res: Response, next: NextFunction) => {
  res.setHeader('X-Plaid-Environment', plaidEnv);
  res.setHeader('X-Simulation-Mode', 'real-time');
  res.setHeader('X-Simulation-Type', 'production-like');
  next();
};

// Apply middleware to all routes
router.use(ensureSandboxEnvironment);
router.use(addRealTimeSimulationHeaders);

// Get real-time simulation configuration (no auth required for info)
router.get('/config', (req: Request, res: Response, next: NextFunction) => {
  getRealTimeSimulationConfigController(req, res).catch(next);
});

// Simulate a single transfer with real-time behavior
router.post('/transfers/:transferId/simulate-realtime', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateRealTimeTransactionController(req, res).catch(next);
});

// Simulate all pending transfers with real-time behavior
router.post('/simulate-all-realtime', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateAllPendingRealTimeController(req, res).catch(next);
});

// Get transaction timeline and webhook history
router.get('/transfers/:transferId/timeline', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  getTransactionTimelineController(req, res).catch(next);
});

export default router;
