import { Router, Request, Response, NextFunction } from 'express';
import {
  simulateTransferSuccess,
  simulateTransferFailure,
  simulateTransferEvent,
  getPendingTransfersController,
  simulateMultipleTransfersController,
  getSimulationInfoController,
  settleAllPendingTransfersController
} from '../controllers/plaidTransferSimulationController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { plaidEnv } from '../config/plaidConfig';
import { sendError } from '../utils/response';

const router = Router();

/**
 * Middleware to ensure simulation endpoints are only available in sandbox/development
 */
const ensureSandboxEnvironment = (req: Request, res: Response, next: NextFunction) => {
  if (plaidEnv !== 'sandbox' && process.env.NODE_ENV !== 'development') {
    return sendError(res, 'Transfer simulation is only available in sandbox/development environment', 403);
  }
  next();
};

/**
 * Middleware to add simulation headers for identification
 */
const addSimulationHeaders = (req: Request, res: Response, next: NextFunction) => {
  res.setHeader('X-Plaid-Environment', plaidEnv);
  res.setHeader('X-Simulation-Mode', 'true');
  next();
};

// Apply environment check and headers to all simulation routes
router.use(ensureSandboxEnvironment);
router.use(addSimulationHeaders);

// Get simulation environment info (no auth required for info endpoint)
router.get('/info', (req: Request, res: Response, next: NextFunction) => {
  getSimulationInfoController(req, res).catch(next);
});

// Get pending transfers that can be simulated
router.get('/pending-transfers', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  getPendingTransfersController(req, res).catch(next);
});

// Simulate transfer success (posted)
router.post('/transfers/:transferId/success', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateTransferSuccess(req, res).catch(next);
});

// Simulate transfer failure
router.post('/transfers/:transferId/failure', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateTransferFailure(req, res).catch(next);
});

// Generic simulate transfer event endpoint
router.post('/transfers/:transferId/simulate', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateTransferEvent(req, res).catch(next);
});

// Simulate multiple transfers at once
router.post('/transfers/simulate-multiple', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  simulateMultipleTransfersController(req, res).catch(next);
});

// Settle all pending transfers (simulate success for all)
router.post('/settle-all', authenticateTokenAndSession, (req: Request, res: Response, next: NextFunction) => {
  settleAllPendingTransfersController(req, res).catch(next);
});

export default router;
